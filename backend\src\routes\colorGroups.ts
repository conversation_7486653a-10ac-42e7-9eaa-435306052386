import express from 'express';
import { 
  getColorGroups, 
  createColorGroup, 
  updateColorGroup, 
  deleteColorGroup 
} from '../controllers/colorGroupController';
import { auth } from '../middleware/auth';

const router = express.Router();

// 所有颜色组路由都需要认证
router.use(auth);

// 获取颜色组列表
router.get('/', getColorGroups);

// 创建颜色组
router.post('/', createColorGroup);

// 更新颜色组
router.put('/:id', updateColorGroup);

// 删除颜色组
router.delete('/:id', deleteColorGroup);

export default router; 