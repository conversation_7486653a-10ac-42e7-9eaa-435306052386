import mongoose, { Document, Schema } from 'mongoose';

export interface IColorGroup extends Document {
  name: string;
  type: 'public' | 'private';
  userId: mongoose.Types.ObjectId;
  colors: Array<{
    primaryColor: string;
    secondaryColor: string;
    bg: string;
  }>;
  createTime: Date;
  updateTime: Date;
}

const ColorGroupSchema = new Schema<IColorGroup>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  type: {
    type: String,
    enum: ['public', 'private'],
    required: true
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  colors: {
    type: [{
      primaryColor: { type: String, required: true },
      secondaryColor: { type: String, required: true },
      bg: { type: String, required: true }
    }],
    required: true,
    validate: {
      validator: function(colors: Array<{primaryColor: string, secondaryColor: string, bg: string}>) {
        return colors.length > 0 && colors.length <= 10;
      },
      message: '颜色数量必须在1-10个之间'
    }
  }
}, {
  timestamps: {
    createdAt: 'createTime',
    updatedAt: 'updateTime'
  }
});

// 创建复合索引确保名称在同一类型下唯一
ColorGroupSchema.index({ name: 1, type: 1, userId: 1 }, { unique: true });

export default mongoose.model<IColorGroup>('ColorGroup', ColorGroupSchema); 