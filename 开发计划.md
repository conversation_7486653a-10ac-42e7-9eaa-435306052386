# XK矩阵项目开发计划

## 技术栈选择

### 前端技术
- **框架**: Vue.js 3 + TypeScript
- **UI组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **构建工具**: Vite

### 后端技术
- **框架**: Node.js + Express.js
- **API设计**: RESTful API
- **数据库**: MongoDB (文档型数据库适合存储矩阵结构)
- **身份认证**: JWT (JSON Web Token)
- **ORM**: Mongoose
- **API文档**: Swagger

### DevOps
- **容器化**: Docker
- **容器编排**: Docker Compose
- **CI/CD**: GitHub Actions
- **代码版本控制**: Git

## 当前开发进度

### 已完成功能
- **X矩阵基础组件**
  - 矩阵布局与可视化（10:9比例长方形）
  - 四个区域的文字标签（产品与服务、市场与客户、目标、愿景/目的）
  - X形对角线显示
  - 分析点的添加、编辑、删除功能
  - 编辑对话框和确认对话框

- **K矩阵基础组件**
  - 矩阵布局与可视化（与X矩阵统一尺寸）
  - 三个区域的文字标签（竞争优势、竞争对手、客户选购标准）
  - K形线条显示
  - 分析点的添加、编辑、删除功能
  - 编辑对话框和确认对话框

- **后端基础架构**
  - Express项目结构
  - 数据库模型设计
  - 基础API实现
  - 用户认证功能

### 已完成的高级功能
- **任意位置添加功能** ✅ - 每个条目都有独立添加按钮，支持在任意位置插入
- **条目拖拽排序** ✅ - 支持在同一维度内拖拽重新排列条目
- **市场条目多彩化** ✅ - 每个市场/客户条目都有独特颜色标识
- **颜色控制面板** ✅ - 实时调整和预览条目颜色

### 待完成功能
- 竞争优势自动化功能
- 高级数据导出功能（PDF、Excel、图片）
- 版本管理功能
- 多用户实时协作
- 权限管理系统
- 生产环境部署

## 迭代开发计划

### 迭代0: 项目初始化与环境搭建 (已完成)

#### 目标
- 搭建前端开发环境
- 创建项目基础结构

#### 完成情况
- 前端项目骨架已创建
- Vue 3 + TypeScript环境已配置
- Element Plus已集成
- 基础路由已设置

### 迭代1: XK矩阵基础功能 (已完成)

#### 目标
- 实现X矩阵和K矩阵的基础可视化
- 实现分析点管理功能

#### 完成情况
- X矩阵和K矩阵组件已创建
- 矩阵布局和视觉设计已完成
- 分析点的添加、编辑、删除功能已实现
- 对话框和确认框已实现

### 迭代2: 后端开发与数据持久化 (已完成)

#### 目标
- 建立后端项目结构
- 设计数据库模型
- 实现数据持久化API
- 前后端集成准备

#### 完成情况
- 后端项目结构已创建
- 数据库模型已设计
- 基础API已实现
- 用户认证功能已实现

### 迭代3: 项目详情页面重构 (已完成✅)

#### 目标
- 重新开发项目详情页面，专注于XK矩阵展示
- 实现符合需求文档的表格状布局
- 确保条目从矩阵边缘向外排列的核心设计

#### 完成情况
✅ **项目详情页面基础架构**
   - 删除旧的项目详情页面代码
   - 创建新的简洁项目详情页面
   - 实现项目基本信息展示和编辑
   - 集成XKMatrixUnified组件

✅ **XK矩阵布局重构**
   - 实现双矩阵水平并列布局
   - 确保X矩阵和K矩阵中心对齐
   - 统一两个矩阵的视觉风格
   - 实现响应式布局适配

✅ **条目排列系统重构**
   - 实现严格向外扩展的条目排列
   - 确保条目从矩阵边缘开始，无间隙连接
   - 形成清晰的表格状结构
   - 支持自动布局适应

✅ **交汇点功能实现**
   - 实现网格交汇系统
   - 支持点击交汇点切换关联状态
   - 提供明确的视觉状态表示
   - 实现实时数据同步

#### 交付物
- ✅ 重构后的项目详情页面
- ✅ 符合需求的XK矩阵布局
- ✅ 完整的条目管理功能
- ✅ 交汇点关联功能

### 迭代4: XK矩阵详细功能开发 (已完成✅)

#### 目标
- 完善XK矩阵的所有维度功能
- 实现完整的条目管理系统
- 优化用户交互体验
- 确保数据持久化稳定性

#### 完成情况
✅ **X矩阵四维度完善**
   - 上方：产品与服务条目管理
   - 左侧：目标条目管理
   - 右侧：市场与客户条目管理
   - 下方：目标/愿景条目管理

✅ **K矩阵三维度完善**
   - 上方：竞争优势条目管理
   - 右侧：竞争对手条目管理
   - 下方：客户选购标准条目管理

✅ **条目管理功能**
   - 添加条目：每个维度的"+"按钮
   - 编辑条目：直接点击编辑，实时保存
   - 删除条目：确认删除，自动调整布局
   - 条目验证：名称非空，长度限制

✅ **数据持久化优化**
   - 实时保存条目变更
   - 交汇点状态同步
   - 错误处理和重试机制
   - localStorage本地持久化

#### 交付物
- ✅ 完整的XK矩阵功能
- ✅ 稳定的条目管理系统
- ✅ 优化的用户体验

### 迭代5: 关联关系管理功能 (已完成)

#### 目标
- 实现X矩阵关联关系管理 ✅
- 实现K矩阵关联关系管理 ✅
- 实现关联关系规则限制 ✅
- 实现竞争优势自动化功能 (计划中)

#### 任务
1. 后端开发 ✅
   - 优化关联关系数据模型 ✅
   - 实现关联关系API ✅
   - 实现竞争优势自动化逻辑 (计划中)

2. 前端开发 ✅
   - 创建关联关系可视化组件 ✅
   - 实现关联关系设置界面 ✅
   - 实现关联关系规则限制 ✅
   - 愿景/目的与市场不能关联 ✅
   - 客户选购标准与市场能够关联 ✅
   - 优化用户界面和交互体验 ✅

3. 测试 ✅
   - 测试关联关系功能 ✅
   - 测试关联关系规则限制 ✅
   - 进行用户体验测试 ✅

#### 交付物
- 关联关系管理功能 ✅
- 关联关系规则限制系统 ✅
- 竞争优势自动化功能 (计划中)
- 优化后的用户体验 ✅

### 迭代6: 关键问题修复与稳定性优化 (已完成✅)

#### 目标
- 修复自动关联问题
- 修复关联关系移动问题
- 优化系统稳定性和用户体验

#### 完成情况
✅ **自动关联问题修复**（2024-12-19）
   - 识别并解决XKMatrixProper.vue中的硬编码默认关联
   - 修复XKMatrix.vue中initializeRelations函数的问题
   - 确保新添加条目不会自动产生关联关系

✅ **关联关系移动问题修复**（2024-12-19）
   - 解决添加条目时关联关系错位的核心问题
   - 实现索引调整机制（adjustIntersectionsForInsert/Delete）
   - 确保删除条目时正确处理相关关联关系

✅ **系统稳定性优化**
   - 完善错误处理和边界情况
   - 优化数据一致性验证
   - 改进用户操作反馈

#### 交付物
- ✅ 稳定可靠的XK矩阵系统
- ✅ 正确的关联关系管理
- ✅ 优化的用户体验

### 迭代7: 高级用户体验功能 (已完成✅)

#### 目标
- 实现任意位置添加条目功能
- 实现条目拖拽排序功能
- 实现市场条目多彩化功能
- 实现颜色控制面板

#### 完成情况
✅ **任意位置添加功能**（2024-12-20）
   - 为每个条目添加独立的"+"按钮
   - 支持在任意位置插入新条目
   - 实现插入时的索引调整机制
   - 确保关联关系正确处理

✅ **条目拖拽排序功能**（2024-12-20）
   - 实现HTML5拖拽API的条目排序
   - 添加拖拽过程的视觉反馈和位置指示
   - 实现拖拽后的关联关系自动调整
   - 支持所有维度的条目拖拽

✅ **市场条目多彩化功能**（2024-12-20）
   - 为每个市场/客户条目分配独特颜色
   - 实现颜色的持久化存储
   - 提供预设颜色方案
   - 支持自定义颜色选择

✅ **颜色控制面板**（2024-12-20）
   - 位于XK矩阵上方的颜色管理界面
   - 实时颜色调整和预览功能
   - 颜色变化即时反映在矩阵中
   - 支持颜色重置和批量调整

#### 交付物
- ✅ 完整的任意位置添加功能
- ✅ 稳定的拖拽排序系统
- ✅ 美观的多彩化条目显示
- ✅ 直观的颜色控制面板

### 迭代8: 高级功能开发 (计划中)

#### 目标
- 实现竞争优势自动化功能
- 实现高级数据导出功能
- 实现版本管理功能

#### 任务
1. 后端开发
   - 设计版本管理数据模型
   - 实现多格式数据导出API
   - 实现版本管理和对比API
   - 实现竞争优势自动化逻辑

2. 前端开发
   - 实现PDF、Excel等格式导出
   - 实现版本管理界面
   - 实现版本对比功能
   - 实现竞争优势自动化界面

3. 测试
   - 测试多格式导出功能
   - 测试版本管理功能
   - 测试竞争优势自动化
   - 进行综合功能测试

#### 交付物
- 竞争优势自动化功能
- 多格式数据导出功能
- 版本管理功能

### 迭代9: 协作功能开发 (计划中)

#### 目标
- 实现多用户实时协作
- 开发权限管理系统
- 支持团队工作空间

#### 任务
1. 后端开发
   - 实现WebSocket实时同步
   - 设计权限管理系统
   - 开发团队工作空间API

2. 前端开发
   - 实现实时协作界面
   - 开发权限管理界面
   - 实现冲突解决机制

3. 测试
   - 测试多用户协作功能
   - 测试权限管理
   - 进行并发测试

#### 交付物
- 多用户实时协作功能
- 权限管理系统
- 团队工作空间

### 迭代10: 系统优化与部署 (计划中)

#### 目标
- 优化系统性能和用户体验
- 准备系统部署
- 编写文档

#### 任务
1. 系统优化
   - 优化前端性能
   - 优化后端API性能
   - 完善错误处理
   - 优化移动端适配

2. 部署准备
   - 配置Docker环境
   - 准备生产环境部署脚本
   - 配置自动化部署流程

3. 文档编写
   - 编写用户手册
   - 编写API文档
   - 编写部署文档

#### 交付物
- 优化后的系统
- 生产环境部署配置
- 完整的项目文档

## Docker配置

### 开发环境配置

```yaml
# docker-compose.dev.yml
version: '3'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "8080:8080"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - MONGO_URI=mongodb://mongo:27017/xk_matrix
    depends_on:
      - mongo

  mongo:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:
```

### 生产环境配置

```yaml
# docker-compose.prod.yml
version: '3'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
    restart: always
    depends_on:
      - backend

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    ports:
      - "3000:3000"
    restart: always
    environment:
      - NODE_ENV=production
      - MONGO_URI=mongodb://mongo:27017/xk_matrix
    depends_on:
      - mongo

  mongo:
    image: mongo:latest
    volumes:
      - mongo_data:/data/db
    restart: always

volumes:
  mongo_data:
```

### 前端Dockerfile (开发环境)

```dockerfile
# frontend/Dockerfile.dev
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./

RUN npm install

COPY . .

EXPOSE 8080

CMD ["npm", "run", "dev"]
```

### 前端Dockerfile (生产环境)

```dockerfile
# frontend/Dockerfile.prod
FROM node:16-alpine as build

WORKDIR /app

COPY package*.json ./

RUN npm install

COPY . .

RUN npm run build

FROM nginx:alpine

COPY --from=build /app/dist /usr/share/nginx/html

COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 后端Dockerfile (开发环境)

```dockerfile
# backend/Dockerfile.dev
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./

RUN npm install

COPY . .

EXPOSE 3000

CMD ["npm", "run", "dev"]
```

### 后端Dockerfile (生产环境)

```dockerfile
# backend/Dockerfile.prod
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./

RUN npm install --production

COPY . .

EXPOSE 3000

CMD ["node", "src/server.js"]
```

## 当前开发重点：高级功能开发

### 近期优先事项（迭代7）
1. **竞争优势自动化功能** ⚡
   - 实现客户选购标准与竞争对手关联分析
   - 自动识别竞争优势机会点
   - 提供智能建议和提示

2. **高级数据导出功能** ⚡
   - 支持PDF格式专业报告导出
   - 支持Excel格式数据导出
   - 支持图片格式可视化导出
   - 自定义导出模板

3. **版本管理系统** ⚡
   - 实现矩阵版本保存和管理
   - 支持版本间对比和差异显示
   - 版本回滚和恢复功能

### 中期目标
1. **实现XK矩阵整合视图**
   - 设计整合视图界面
   - 实现整合视图交互
   - 优化视觉效果

2. **实现数据导出和报告生成**
   - 支持多种导出格式
   - 实现报告模板
   - 优化导出体验

### 长期目标
1. **实现版本管理功能**
   - 设计版本管理机制
   - 实现版本对比功能
   - 优化版本管理体验

2. **系统优化与部署**
   - 性能优化
   - 移动端适配
   - 生产环境部署

## 开发进度跟踪

本开发计划将根据项目进展进行实时更新，主要更新点包括：

1. 每个迭代结束时更新实际完成情况
2. 根据实际开发进度调整后续迭代计划
3. 记录遇到的技术难点和解决方案
4. 更新技术栈或工具选择（如有变更）

更新将由开发团队在每个迭代结束时进行审核和确认，确保计划与实际开发保持一致。 

## 项目概述
基于用户提供的效果图，开发一个符合实际业务需求的XK矩阵管理系统。系统采用类似电子表格的界面设计，支持动态添加分析点，实现各分析点间的关系管理。

## 技术架构分析（已完成✅）

### 数据库设计优化
✅ **分析点结构增强**
- 增加 `_id` 字段支持唯一标识
- 增加 `order` 字段支持显示顺序控制
- 描述字段支持更详细的业务说明

✅ **关系存储机制重构**
- X矩阵：采用坐标系统 (rowType, rowIndex, colType, colIndex, value)
- K矩阵：采用类别索引系统 (category1, index1, category2, index2, value)
- 支持精确的交叉点关系管理

✅ **前端界面架构**
- 表格矩阵布局，符合用户效果图需求
- 中心X/K图形保持核心位置
- 动态行列扩展支持
- 响应式设计适配移动端

## 功能实现进度

### 核心矩阵功能
✅ **X矩阵表格化实现**
- 目标作为行标题（左侧）
- 产品与服务作为列标题（顶部）
- 市场与客户作为右侧列
- 愿景/目的作为底部行
- 交叉点复选框关系管理

✅ **数据持久化**
- 关系矩阵实时保存
- 分析点CRUD操作
- 项目级别的数据隔离

### 业务数据验证
根据用户效果图，系统应支持：

**目标模块（左侧行）**：
- 2023年营业收入1亿元
- 2023年净利润为正 
- 预计市值不低于10亿

**产品与服务模块（顶部列）**：
- 作战指挥系统
- 民用DSS决策支持系统
- 大数据分析解决方案

**市场与客户模块（右侧列）**：
- 军兵种/军事院校
- 央企/国企/四行/三通
- 民企

**愿景/目的模块（底部行）**：
- 2024科创板IPO

## 技术债务与风险

### 已解决问题
✅ **关系存储效率** - 从字符串关联改为坐标索引系统
✅ **界面布局适配** - 实现类似Excel的矩阵表格
✅ **数据一致性** - 增加顺序字段和ID管理

### 待优化项目
🔄 **K矩阵组件适配** - 需要同步更新K矩阵的表格化设计
🔄 **批量操作支持** - 大量分析点时的性能优化
🔄 **导入导出功能** - 支持Excel格式的数据交换

## 开发里程碑

### 第一阶段：核心架构 ✅
- [x] 数据模型重构
- [x] X矩阵表格化实现
- [x] 关系管理系统
- [x] 基础CRUD操作

### 第二阶段：功能完善 🔄
- [ ] K矩阵表格化改造
- [ ] 批量数据操作
- [ ] 高级关系分析
- [ ] 数据可视化增强

### 第三阶段：用户体验 📋
- [ ] 界面交互优化
- [ ] 性能调优
- [ ] 移动端适配完善
- [ ] 用户培训文档

## 技术决策记录

### 2024-01-XX 数据库架构重构
**决策**：采用坐标系统存储矩阵关系
**原因**：用户效果图显示的是精确的表格交叉点关系，需要支持 (行,列) 坐标定位
**影响**：提高查询效率，支持复杂关系分析

### 2024-01-XX 界面设计方向
**决策**：从标签式设计改为表格矩阵设计
**原因**：用户效果图明确显示类似Excel的表格结构需求
**影响**：更符合用户习惯，支持直观的关系编辑

## 项目现状总结
当前系统已完成核心架构设计，数据库和前端组件都已按照用户效果图需求进行了重构。X矩阵组件实现了表格化界面，支持动态添加分析点和关系管理。下一步需要继续完善K矩阵组件和用户体验优化。

## XK矩阵页面开发详细步骤

### 第一步：项目详情页面基础架构 ✅
- [x] 删除旧的ProjectDetail.vue文件
- [x] 创建新的简洁项目详情页面
- [x] 实现项目基本信息展示
- [x] 添加项目编辑和删除功能
- [x] 集成XKMatrixNew组件
- [x] 实现加载状态和错误处理
- [x] 添加返回按钮和页面导航

### 第二步：XK矩阵布局核心修正 ✅
- [x] 修正条目排列逻辑，确保从矩阵边缘开始
- [x] 统一X矩阵和K矩阵的条目尺寸
- [x] 实现表格状的交汇网格结构
- [x] 优化双矩阵的水平对齐
- [x] 修正X矩阵中心位置为300px，K矩阵中心位置为700px
- [x] 更新所有条目的位置计算以适应新的布局

### 第三步：条目管理功能完善 ✅
- [x] 完善添加条目功能
- [x] 优化编辑条目交互
- [x] 实现编辑对话框和表单验证
- [x] 添加编辑状态管理和保存功能
- [ ] 改进删除条目确认流程
- [ ] 实现条目验证机制

### 第四步：交汇点功能实现 ✅
- [x] 实现精确的交汇点定位
- [x] 添加点击切换关联状态
- [x] 设计清晰的视觉状态表示
- [x] 实现交汇点数据的实时保存
- [x] 添加交汇点详情对话框
- [x] 实现关联强度、描述和重要程度设置

### 第五步：用户体验优化 ✅
- [x] 优化响应式布局
- [x] 改进加载和错误状态
- [x] 添加操作反馈提示
- [x] 完善键盘导航支持
- [x] 添加删除确认对话框
- [x] 实现自定义加载动画和进度条

### 第六步：数据持久化测试 ✅
- [x] 测试条目的增删改查
- [x] 验证交汇点状态保存
- [x] 实现localStorage数据持久化
- [x] 确保数据一致性
- [x] 添加数据验证功能
- [x] 实现数据导出功能

---

## 开发完成总结 🎉

### 已完成的主要功能（截至2024年12月20日）
1. **完整的XK矩阵系统** ✅ - 实现了双矩阵水平并列布局，条目从矩阵边缘向外扩展
2. **全面的条目管理** ✅ - 完整的增删改查功能，支持所有7种类型的条目
3. **高级条目功能** ✅ - 任意位置添加、拖拽排序、多彩化显示、颜色控制面板
4. **智能关联关系管理** ✅ - 可点击交汇点切换关联状态，支持关联规则限制
5. **响应式设计** ✅ - 支持多种屏幕尺寸，移动端友好
6. **稳定的数据持久化** ✅ - 基于localStorage的本地数据保存，支持JSON导出
7. **优质用户体验** ✅ - 加载动画、错误处理、键盘导航、操作反馈、拖拽反馈
8. **项目管理功能** ✅ - 项目创建、编辑、删除、搜索和筛选
9. **后端基础架构** ✅ - Express.js + MongoDB + JWT认证系统

### 关键技术问题修复
✅ **自动关联问题修复**（2024-12-19）- 移除硬编码默认关联，确保用户完全控制关联关系
✅ **关联关系移动问题修复**（2024-12-19）- 实现索引调整机制，确保添加/删除条目时关联关系正确保持
✅ **任意位置添加功能**（2024-12-20）- 实现每个条目的独立添加按钮和插入机制
✅ **拖拽排序功能**（2024-12-20）- 实现HTML5拖拽API的条目重排序功能
✅ **多彩化显示功能**（2024-12-20）- 实现市场条目的独特颜色标识和控制面板

### 技术特点
- Vue 3 + TypeScript + Element Plus
- 表格状矩阵布局设计
- 组件化架构（XKMatrixUnified.vue）
- 完整的数据验证和导出功能
- 智能关联关系管理
- 响应式设计适配

### 当前系统状态
- ✅ **核心功能完备**：所有基础XK矩阵功能已实现并稳定运行
- ✅ **用户体验优秀**：界面友好，操作直观，响应快速
- ✅ **数据一致性良好**：关联关系管理正确，数据持久化稳定
- ✅ **技术架构合理**：前后端分离，可扩展性强

### 下一步发展方向
🔄 **高级功能开发**：多格式导出、版本管理、竞争优势分析报告
🔄 **协作功能**：多用户实时协作、权限管理、团队工作空间
🔄 **系统优化**：性能优化、生产环境部署、API集成

## 最新开发进展（2024年12月21日）

### 竞争优势自动化功能完成 ✅

#### 功能概述
实现了智能的竞争优势抽取功能，能够自动识别并添加潜在的竞争优势点。

#### 核心特性
1. **智能筛选逻辑**
   - 只抽取"未被添加为竞争优势"的客户选购标准
   - 只抽取"未关联全部竞争对手"的客户选购标准
   - 按与竞争对手关联数量从少到多排序，优先抽取关联最少的

2. **名称去重机制**
   - 对客户选购标准名称进行去重（忽略大小写和空格）
   - 相同名称的客户选购标准只抽取一条到竞争优势中
   - 优先保留关联竞争对手最少的条目

3. **用户界面优化**
   - 头部按钮："抽取竞争优势"
   - 智能按钮状态：只有当存在可抽取标准时才启用
   - 实时提示："(X个可抽取标准)"
   - 操作反馈：显示抽取成功的条目数量

4. **技术实现细节**
   - 名称比对：去除前后空格并统一小写，确保比对准确性
   - 去重算法：使用Set数据结构，按名称去重
   - 排序逻辑：按与竞争对手关联数量升序排序
   - 数据持久化：自动保存到后端数据库
   - 页面刷新：操作完成后自动刷新页面数据

#### 业务价值
- 帮助企业快速识别潜在的竞争优势点
- 减少手动分析的工作量
- 确保竞争优势的完整性和准确性
- 支持战略决策的数据驱动分析

---
*文档更新时间：2024年12月21日*
*开发状态：XK矩阵核心功能、高级用户体验功能及竞争优势自动化功能开发完成 ✅*