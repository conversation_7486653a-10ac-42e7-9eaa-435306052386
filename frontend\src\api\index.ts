import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api', // 使用相对路径，自动使用当前域名
  timeout: 5000
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response && error.response.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 项目相关API
export const projectApi = {
  // 获取项目列表
  getProjects() {
    return api.get('/projects')
  },

  // 获取项目详情
  getProject(id: string | number) {
    return api.get(`/projects/${id}`)
  },

  // 创建项目
  createProject(data: any) {
    return api.post('/projects', data)
  },

  // 更新项目
  updateProject(id: string | number, data: any) {
    return api.put(`/projects/${id}`, data)
  },

  // 删除项目
  deleteProject(id: string | number) {
    return api.delete(`/projects/${id}`)
  },

  // 获取X矩阵数据
  getXMatrix(projectId: string | number) {
    return api.get(`/projects/${projectId}/xmatrix`)
  },

  // 更新X矩阵数据
  updateXMatrix(projectId: string | number, data: any) {
    return api.put(`/projects/${projectId}/xmatrix`, data)
  },

  // 获取K矩阵数据
  getKMatrix(projectId: string | number) {
    return api.get(`/projects/${projectId}/kmatrix`)
  },

  // 更新K矩阵数据
  updateKMatrix(projectId: string | number, data: any) {
    return api.put(`/projects/${projectId}/kmatrix`, data)
  },

  // 同步XK矩阵数据到明道云
  syncToMingdao(data: any) {
    return api.post('/projects/sync-to-mingdao', data)
  }
}

// 用户相关API
export const userApi = {
  // 用户登录
  login(data: { username: string; password: string }) {
    return api.post('/auth/login', data)
  },

  // 用户注册
  register(data: { username: string; name: string; email: string; password: string }) {
    return api.post('/auth/register', data)
  },

  // 获取当前用户信息
  getCurrentUser() {
    return api.get('/auth/me')
  }
}

// 颜色组相关API
export const colorGroupApi = {
  // 获取颜色组列表
  getColorGroups() {
    return api.get('/color-groups')
  },

  // 创建颜色组
  createColorGroup(data: any) {
    return api.post('/color-groups', data)
  },

  // 更新颜色组
  updateColorGroup(id: string | number, data: any) {
    return api.put(`/color-groups/${id}`, data)
  },

  // 删除颜色组
  deleteColorGroup(id: string | number) {
    return api.delete(`/color-groups/${id}`)
  }
}

export default api 