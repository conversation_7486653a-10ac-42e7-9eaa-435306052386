# XK矩阵项目需求文档

## 1. 项目概述

### 1.1 背景
XK矩阵是一种企业战略制定工具，通过结构化的方式帮助企业理清战略目标、产品服务、市场客户以及竞争优势之间的关系，从而形成有效的企业战略。

### 1.2 目标
开发一个简单的项目管理系统，使用户能够查看项目列表和项目的XK模型。系统将帮助企业可视化地创建、编辑和管理其战略规划，识别关键竞争优势，并制定相应的战略措施。

### 1.3 参考原型
本文档基于XK矩阵结构图进行需求分析和功能设计，原型图展示了XK矩阵的基本结构和交互方式。

## 2. 功能需求

### 2.1 项目管理功能
1. **项目列表**
   - 显示所有项目的列表视图
   - 提供项目基本信息（如项目名称、创建日期、负责人等）
   - 支持项目搜索和筛选功能
   
2. **项目详情**
   - 点击项目列表中的项目可进入项目详情页
   - 在项目详情页中显示该项目的XK矩阵
   - 支持查看和编辑项目基本信息

### 2.2 X矩阵功能
1. **四个核心模块**
   - 愿景/目的（下方）：企业的总体目标和定位
   - 目标（左方）：具体可量化的战略目标，如营收入1亿元、利润为正等
   - 产品与服务（上方）：企业提供的产品和服务组合，如作战指挥系统、大数据解决方案等
   - 市场与客户（右方）：目标市场和客户群体

2. **分析点管理**
   - 每个模块支持添加任意数量的分析点
   - 支持通过点击"+"按钮添加新的分析点
   - 每个分析点包含名称和描述两个字段
   - 支持分析点的创建、编辑、删除操作

3. **矩阵可视化**
   - 矩阵内部区域采用10:9比例的长方形
   - X矩阵中心区域显示对角线（X形状）
   - 四个区域文字标签位置：产品与服务在上方，市场与客户在右边，愿景在下方，目标在左边

### 2.3 K矩阵功能
1. **三个核心模块**
   - 竞争优势（上方）：企业相对于竞争对手的优势点，如技术领先、人才优势等
   - 竞争对手（右方）：主要的市场竞争者
   - 客户选购标准（下方）：客户选择产品或服务的关键因素，如性价比、技术水平等

2. **分析点管理**
   - 每个模块支持添加任意数量的分析点
   - 支持通过点击"+"按钮添加新的分析点
   - 每个分析点包含名称和描述两个字段
   - 支持分析点的创建、编辑、删除操作

3. **矩阵可视化**
   - 矩阵内部区域采用与X矩阵一致的10:9比例长方形
   - K矩阵中心区域显示从左边中点到右边顶点的线条（K形状）
   - 三个区域文字标签位置：竞争优势在上方，竞争对手在右边，客户选购标准在下方

### 2.4 数据管理功能
1. **数据保存**
   - 自动保存用户对矩阵的修改
   - 支持分析点的持久化存储

2. **数据验证**
   - 对输入数据进行验证，确保名称不为空
   - 提供操作反馈，如添加成功、编辑成功等

3. **操作确认**
   - 删除操作需要确认对话框防止误操作
   - 取消操作可以回到上一步

### 2.5 XK矩阵前端页面详细设计

#### 2.5.1 整体布局要求
1. **双矩阵并列布局**
   - X矩阵和K矩阵在同一页面水平排列，中心对齐
   - 两个矩阵使用统一的视觉风格，无需特别区分功能差异
   - 矩阵间距适中，保持视觉平衡
   - 支持响应式布局，适应不同屏幕尺寸

2. **矩阵中心区域**
   - 每个矩阵中心都有蓝色矩形作为核心标识区域
   - 中心矩形保持固定的宽高比例
   - 作为整个矩阵的视觉焦点和布局基准点

#### 2.5.2 条目排列核心规则
1. **严格向外扩展原则**
   - 所有条目必须从矩阵边缘开始，向外依次排列
   - 第一个条目紧贴矩阵边缘，后续条目紧密相连
   - 绝对不允许条目与矩阵中心之间有间隙

2. **表格状结构形成**
   - 水平条目（上方/下方）：水平排列，形成行结构
   - 垂直条目（左侧/右侧）：垂直排列，形成列结构
   - 水平和垂直条目的交汇处形成网格状的交汇点

3. **自动布局适应**
   - 添加新条目时，布局自动调整保持表格结构
   - 同方向的条目保持统一的宽度或高度
   - 条目数量变化时，整体布局保持居中对齐

#### 2.5.3 X矩阵具体布局（四个维度）
- **上方维度**：产品与服务条目，水平排列
- **左侧维度**：目标条目，垂直排列
- **右侧维度**：市场与客户条目，垂直排列
- **下方维度**：目标/愿景条目，水平排列

#### 2.5.4 K矩阵具体布局（三个维度）
- **上方维度**：竞争优势条目，水平排列
- **右侧维度**：竞争对手条目，垂直排列
- **下方维度**：客户选购标准条目，水平排列
- **左侧维度**：空白区域（K矩阵只有三个维度）

#### 2.5.5 交汇点功能设计
1. **网格交汇系统**
   - 水平和垂直条目的交汇处形成可点击的网格单元
   - 交汇点可以标记不同维度条目间的关联关系
   - 用户可以点击交汇点来切换关联状态

2. **关联关系规则**
   - **X矩阵关联规则**：
     - 产品与服务 ↔ 目标：可以关联
     - 产品与服务 ↔ 市场与客户：可以关联
     - 愿景/目的 ↔ 目标：可以关联
     - **愿景/目的 ↔ 市场与客户：不能关联**（显示为禁用状态）
   - **K矩阵关联规则**：
     - 竞争优势 ↔ 竞争对手：可以关联
     - 竞争优势 ↔ 客户选购标准：可以关联
     - **客户选购标准 ↔ 市场与客户：可以关联**（跨矩阵关联）

3. **视觉状态表示**
   - 已关联的交汇点有明显的视觉标识（如背景色变化、勾选标记）
   - 未关联的交汇点保持默认状态
   - 不能关联的交汇点显示为禁用状态（灰色背景，×标记）
   - 鼠标悬停时提供视觉反馈和提示信息

4. **实时数据同步**
   - 交汇点状态变化实时保存到后端
   - 提供操作反馈，确认保存成功

#### 2.5.6 条目管理功能
1. **添加条目**
   - 每个维度末尾有"+"按钮，点击可添加新条目
   - 添加对话框支持输入条目名称和描述
   - 新条目自动排列在该维度的末尾位置

2. **编辑条目**
   - 点击条目可以直接编辑内容
   - 支持实时保存或确认保存
   - 编辑状态有明显的视觉提示

3. **删除条目**
   - 条目上有删除按钮或右键菜单
   - 删除前需要确认对话框防止误操作
   - 删除后自动调整布局

4. **条目验证**
   - 条目名称不能为空
   - 长度限制在合理范围内
   - 重复名称提示警告

### 2.6 已实现的关联关系功能
1. **关联关系管理** ✅
   - 相邻模块间的分析点可以建立关联关系
   - 通过点击交汇点切换关联状态
   - 提供可视化的关联矩阵展示
   - 支持关联关系规则限制

2. **关联关系规则** ✅
   - **X矩阵关联规则**：
     - 产品与服务 ↔ 目标：可以关联 ✅
     - 产品与服务 ↔ 市场与客户：可以关联 ✅
     - 愿景/目的 ↔ 目标：可以关联 ✅
     - **愿景/目的 ↔ 市场与客户：不能关联**（显示为禁用状态）✅
   - **K矩阵关联规则**：
     - 竞争优势 ↔ 竞争对手：可以关联 ✅
     - 竞争优势 ↔ 客户选购标准：可以关联 ✅
     - **客户选购标准 ↔ 市场与客户：可以关联**（跨矩阵关联）✅

3. **基础数据导出** ✅
   - 支持JSON格式数据导出
   - 数据本地持久化存储
   - 数据验证和一致性检查

### 2.7 已实现的高级功能
1. **任意位置添加功能** ✅
   - 每个XK矩阵条目都有独立的添加按钮
   - 支持在任意位置插入新条目
   - 条目可以在同一维度内拖拽重新排序
   - 添加新条目时自动调整布局和关联关系

2. **市场/客户条目多彩化** ✅
   - 每个市场/客户条目都有独特的颜色标识
   - 支持通过颜色控制面板自定义条目颜色
   - 颜色变化实时反映在矩阵布局中
   - 提供预设颜色方案和自定义颜色选择

3. **颜色控制面板** ✅
   - 位于XK矩阵上方的颜色管理界面
   - 支持为每个市场/客户条目设置独立颜色
   - 提供颜色预览和实时更新功能
   - 颜色设置自动保存到数据库

4. **颜色组管理功能** ✅
   - **颜色组保存**：将当前市场/客户条目的颜色配置保存为颜色组
   - **颜色组选择**：从已保存的颜色组中选择并应用到当前项目
   - **颜色组分类**：支持个人颜色组（仅当前用户可见）和公共颜色组（所有用户可见）
   - **权限控制**：只有管理员可以创建和管理公共颜色组
   - **数量限制**：个人颜色组最多8个，公共颜色组最多7个
   - **颜色组管理**：支持重命名和删除颜色组
   - **实时同步**：选择颜色组后自动保存到项目数据库，刷新页面后保持颜色配置

5. **条目拖拽排序** ✅
   - 所有条目支持在同一维度内拖拽移动
   - 拖拽时提供视觉反馈和位置指示
   - 移动后自动保存新的排序
   - 关联关系随条目移动自动调整

### 2.8 竞争优势自动化功能 ✅

#### 2.8.1 功能概述
当客户选购标准和竞争对手之间的某分析点没有关联（未打勾）时，系统应自动在竞争优势模块增加该分析点，标识出竞争对手没有但对客户重要的潜在优势点。

#### 2.8.2 核心业务逻辑
1. **智能筛选条件**
   - 只抽取"未被添加为竞争优势"的客户选购标准
   - 只抽取"未关联全部竞争对手"的客户选购标准（即关联数量 < 竞争对手总数）
   - 按与竞争对手关联数量从少到多排序，优先抽取关联最少的

2. **名称去重机制**
   - 对客户选购标准名称进行去重（忽略大小写和空格）
   - 相同名称的客户选购标准只抽取一条到竞争优势中
   - 优先保留关联竞争对手最少的条目

3. **自动添加规则**
   - 新增的竞争优势不自动与任何客户选购标准或竞争对手建立关联
   - 新增条目带有`source: 'criteria'`标记，区分来源
   - 手动添加的竞争优势标记为`source: 'manual'`

4. **用户界面**
   - 头部按钮："抽取竞争优势"
   - 按钮状态：只有当存在可抽取标准时才启用
   - 显示提示："(X个可抽取标准)"
   - 操作反馈：显示抽取成功的条目数量

#### 2.8.3 技术实现
- 名称比对：去除前后空格并统一小写，确保比对准确性
- 去重算法：使用Set数据结构，按名称去重
- 排序逻辑：按与竞争对手关联数量升序排序
- 数据持久化：自动保存到后端数据库
- 页面刷新：操作完成后自动刷新页面数据

### 2.3.1 客户选购标准（Buying Criterias）最新需求
1. 支持任意数量的客户选购标准条目，条目可添加、编辑、删除，且每条条目有唯一id（由后端分配ObjectId，前端不自定义id）。
2. 客户选购标准可与市场（跨矩阵）、竞争对手、竞争优势建立关联。
3. “抽取竞争优势”功能：
   - 允许内容重复的客户选购标准都能被单独抽取为竞争优势，抽取时用id判断唯一性。
   - 只要某条客户选购标准未被抽取为竞争优势且未关联全部竞争对手，即可被抽取。
   - 抽取后，竞争优势条目会记录来源criteriaId。
   - 按id判断是否已被抽取，不再用名称去重。
   - 按与竞争对手关联数量升序排序，优先抽取关联最少的。
   - 按钮可用性与实际可抽取数量完全一致。
4. 客户选购标准的id字段只允许后端分配的合法ObjectId，前端不再自定义id。
5. 相关前端、后端、数据保存、交互逻辑均已适配上述规则。

### 2.9 待实现功能
1. **高级数据导出**
   - 支持将XK矩阵数据导出为PDF、Excel、图片等格式
   - 支持生成专业的战略分析报告

2. **版本管理**
   - 支持保存多个版本的战略矩阵
   - 提供版本对比功能

3. **协作功能**
   - 多用户实时协作编辑
   - 权限管理和访问控制

## 3. 非功能需求

### 3.1 用户界面
1. **直观性**
   - 界面设计清晰，操作直观
   - 使用视觉化元素表示矩阵关系
   - 矩阵内部添加清晰的线条标识（X形和K形）
   - 统一两个矩阵的尺寸，确保中心区域大小一致并处于同一水平线上

2. **表格状布局要求**
   - 条目必须从矩阵边缘开始，严格向外排列
   - 形成清晰的表格状结构，便于用户理解关联关系
   - 交汇点设计明确，支持直观的点击交互
   - 条目排列整齐，视觉层次清晰

3. **响应式设计**
   - 适配不同尺寸的屏幕（桌面端和平板端）
   - 支持触控操作
   - 在小屏幕上保持矩阵结构的完整性

### 3.2 性能要求
1. **响应时间**
   - 页面加载时间不超过3秒
   - 操作响应时间不超过1秒

2. **并发性**
   - 支持多用户同时访问和编辑
   - 提供适当的并发控制机制

### 3.3 可靠性
1. **数据持久化**
   - 自动保存用户的编辑操作
   - 提供数据备份和恢复功能

2. **错误处理**
   - 优雅处理各类异常情况
   - 提供明确的错误提示

### 3.4 安全性
1. **访问控制**
   - 基于角色的权限管理
   - 支持团队协作和权限分配

2. **数据安全**
   - 数据传输加密
   - 定期数据备份

## 4. 技术架构

### 4.1 前端技术
- Vue.js 3 + TypeScript
- Element Plus UI组件库
- Pinia状态管理
- Vue Router路由管理
- Axios HTTP客户端

### 4.2 后端技术
- Node.js + Express.js
- MongoDB数据库
- Mongoose ORM
- JWT用户认证
- RESTful API设计

### 4.3 部署要求
- 支持Docker容器化部署
- 可选本地部署方案

## 5. 当前开发状态

### 5.1 已实现功能
✅ **核心XK矩阵功能**
- X矩阵和K矩阵的完整可视化（表格状布局）
- 双矩阵水平并列布局，中心对齐
- 条目从矩阵边缘向外扩展的核心设计
- 矩阵内部的视觉设计（X形和K形线条）

✅ **条目管理系统**
- 所有7种类型条目的完整CRUD操作
- 直接点击编辑功能
- 删除确认机制
- 表单验证和数据验证

✅ **高级条目功能**
- 任意位置添加条目（每个条目都有独立的添加按钮）
- 条目拖拽排序（支持在同一维度内重新排列）
- 市场/客户条目多彩化（每个条目独特颜色标识）
- 颜色控制面板（实时调整条目颜色）
- 颜色组管理（保存、选择、管理颜色配置方案）

✅ **关联关系管理**
- 完整的交汇点功能实现
- 点击切换关联状态
- 关联关系规则限制（愿景/目的与市场不能关联等）
- 跨矩阵关联支持（客户选购标准与市场）
- 关联关系随条目移动自动调整

✅ **用户体验优化**
- 响应式设计，支持多种屏幕尺寸
- 加载动画和进度指示
- 操作反馈和错误处理
- 键盘导航支持
- 拖拽操作的视觉反馈

✅ **数据持久化**
- 本地数据自动保存（localStorage）
- 数据导出功能（JSON格式）
- 页面刷新数据保持
- 数据一致性验证
- 颜色设置和排序的持久化
- 颜色组配置的数据库存储和跨项目共享

✅ **项目管理功能**
- 项目列表查看
- 项目创建、编辑、删除
- 项目详情页面
- 搜索和筛选功能

✅ **后端基础架构**
- Express.js + MongoDB
- RESTful API设计
- 用户认证系统（JWT）
- 数据模型设计
- 颜色组管理API（创建、查询、更新、删除颜色组）

### 5.2 关键技术问题修复
✅ **自动关联问题修复**（2024-12-19）
- 移除了硬编码的默认关联关系
- 修复了XKMatrixProper.vue中的初始化默认关联
- 修复了XKMatrix.vue中的initializeRelations函数

✅ **关联关系移动问题修复**（2024-12-19）
- 解决了添加条目时关联关系错位的问题
- 实现了索引调整机制（adjustIntersectionsForInsert/Delete）
- 确保删除条目时正确处理相关关联关系

✅ **任意位置添加功能实现**（2024-12-20）
- 为每个条目添加独立的添加按钮
- 实现条目插入时的索引调整
- 确保新添加条目的关联关系正确处理

✅ **条目拖拽排序功能**（2024-12-20）
- 实现HTML5拖拽API的条目排序
- 添加拖拽过程的视觉反馈
- 确保拖拽后关联关系的正确调整

✅ **市场条目多彩化功能**（2024-12-20）
- 为每个市场/客户条目分配独特颜色
- 实现颜色控制面板界面
- 支持实时颜色调整和预览

### 5.3 下一步开发重点
🔄 **高级功能开发**
- 竞争优势自动化功能
- 高级数据导出（PDF、Excel、图片）
- 版本管理和对比功能

🔄 **协作功能**
- 多用户实时协作
- 权限管理系统
- 团队工作空间

🔄 **系统优化**
- 性能优化和大数据量支持
- 移动端体验优化
- 生产环境部署准备

## 6. 附录

### 6.1 术语表
- **项目管理**：管理企业的各个战略项目
- **X矩阵**：连接愿景/目的、目标、产品与服务、市场与客户的战略规划工具
- **K矩阵**：连接竞争优势、竞争对手、客户选购标准的竞争分析工具
- **分析点**：矩阵中各模块下的具体项目，如"大数据分析解决方案"、"军兵种/军事院校"等
- **关联**：表示两个分析点之间存在逻辑或业务关系
- **颜色组**：市场/客户条目的颜色配置方案，可保存和重复使用
- **个人颜色组**：仅创建者可见的颜色配置方案
- **公共颜色组**：所有用户可见的颜色配置方案，仅管理员可创建

### 6.2 参考原型图
请参考XK矩阵结构图，图中展示了系统的视觉结构和功能示意。

原型图主要展示了:
1. X矩阵的四个方向（目标、产品与服务、市场与客户、愿景/目的）
2. K矩阵的三个方向（竞争优势、竞争对手、客户选购标准）
3. 分析点之间的关联关系（通过勾选表示）
4. 系统的整体布局和交互方式