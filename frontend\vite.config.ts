import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173,
    allowedHosts: true, // 允许所有主机名（包括ngrok域名）
    watch: {
      usePolling: true, // 在Docker容器中使用轮询来监听文件变化
      interval: 1000 // 轮询间隔（毫秒）
    },
    proxy: {
      '/api': {
        target: 'http://backend:3005', // Docker容器内部通信
        changeOrigin: true,
        secure: false
      }
    }
  }
}) 