import express from 'express';
import cors from 'cors';
import connectDB from './config/db';
import config from './config/config';

// 导入路由
import authRoutes from './routes/auth';
import projectRoutes from './routes/projects';
import userRoutes from './routes/users';
import colorGroupRoutes from './routes/colorGroups';

// 连接数据库
connectDB();

// 初始化Express应用
const app = express();

// 中间件
app.use(cors({
  origin: true, // 允许所有域名（测试环境）
  credentials: true
}));
app.use(express.json());

// 请求日志中间件（仅开发环境）
if (config.nodeEnv === 'development') {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.path}`);
    next();
  });
}

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/users', userRoutes);
app.use('/api/color-groups', colorGroupRoutes);

// 错误处理中间件
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('服务器错误:', err);
  res.status(500).json({ message: '服务器内部错误', error: err.message });
});

// 基础路由
app.get('/', (req, res) => {
  res.send('XK矩阵API服务正在运行');
});

// 启动服务器
const PORT = config.port;
app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
});

export default app; 